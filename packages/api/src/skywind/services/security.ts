import { AccessTokenInfo, User, UserPermissions, UserStatus } from "../entities/user";
import { BaseEntity } from "../entities/entity";
import { GameProvider } from "../entities/gameprovider";
import * as Errors from "../errors";
import { apiSwagger } from "../utils/swagger";
import * as TokenUtils from "../utils/token";
import { TwoFATokenData } from "../utils/token";
import * as GameProviderService from "../services/gameprovider";
import * as redis from "../storage/redis";
import * as crypto from "node:crypto";
import config from "../config";
import { Redis as RedisClient } from "ioredis";
import EntityCache from "../cache/entity";
import logger from "../utils/logger";
import { measures, token } from "@skywind-group/sw-utils";
import AccessTokenService from "./accessToken";
import { AuditSwagger } from "../utils/auditHelper";
import { UserModel } from "../models/user";
import measureProvider = measures.measureProvider;

const log = logger();

const authenticator = require("authenticator");
const QRCode = require("qrcode");
const captcha = require("trek-captcha");

const OTP_LENGTH = 6;
const SECONDS_IN_MINUTE = 60;

const randomBytes = (size: number) => new Promise<Buffer>((resolve, reject) => {
    crypto.randomBytes(size, (err, result) => {
        if (err) {
            reject(err);
        } else {
            resolve(result);
        }
    });
});

const SCOPE_ID: string = "Permissions";
const KEY_BASE: string = "sw-management-api";

const loginConfigMapping = {
    user: config.settingsDefaults,
    player: config.playerSettingsLogin
};

interface RedisKeysParts {
    failedPfx: string;
    expiresPfx: string;
    attemptsSfx: string;
}

const getEventKeysParts = (eventType: SECURITY_EVENT_TYPE = SECURITY_EVENT_TYPE.LOGIN): RedisKeysParts => {
    if (eventType === SECURITY_EVENT_TYPE.LOGIN) {
        return {
            failedPfx: "failedLogins",
            expiresPfx: "expiresFailedLogins",
            attemptsSfx: "LoginAttempts",
        };
    } else {
        return {
            failedPfx: "failedPwdResets",
            expiresPfx: "expiresFailedPwdResets",
            attemptsSfx: "PwdResetAttempts"
        };
    }
};

let operations: OperationsInfo;

export enum SECURITY_EVENT_TYPE {
    LOGIN,
    PWD_RESET
}

export enum SECURITY_AUTH_TYPE {
    USER = "user",
    PLAYER = "player"
}

export interface CaptchaInfo {
    csrfToken: string;
    image: string;
}

export interface Captcharized {
    captchaToken?: string;
    csrfToken?: string;
}

export interface KeyEntityHolder {
    keyEntity: BaseEntity;
    currentEntity?: BaseEntity;
}

export interface ProviderHolder {
    provider: GameProvider;
}

export interface PermissionsHolder {
    permissions: UserPermissions;
    isSuperAdmin?: boolean;
}

export interface UserInfoHolder {
    userId: number;
    username: string;
}

export interface SessionHolder {
    sessionId: string;
}

export interface OAuthAuditInfoHolder {
    iat?: number;
    exp?: number;
}

export interface SwaggerHolder {
    swagger: { [key: string]: any } & AuditSwagger;
}

export interface OperationDescription {
    id: string;
    description: string;
    permissions: Array<string>;
}

export interface IpHolder {
    resolvedIp: string;
}

export interface AuditHolder {
    isAudit: boolean;
    audited: boolean;
}

export interface IsLiveGameHolder {
    isLiveGame: boolean;
}

/**
 * Generate access token
 *
 * @param loginInfo
 * @param user
 * @returns access token
 */
export function generateAccessToken(loginInfo: AccessTokenInfo,
                                    user: User): Promise<string> {
    return AccessTokenService.get().generate(loginInfo, user);
}

/**
 * Verify access token and find the main key entity according to it
 * @param accessToken
 */
export async function parseToken(accessToken: string):
    Promise<KeyEntityHolder & PermissionsHolder & UserInfoHolder & SessionHolder> {
    return AccessTokenService.get().verify(accessToken);
}

export async function parseInternalToken(internalToken: string): Promise<any> {
    try {
        return await TokenUtils.verifyInternalToken(internalToken);
    } catch (err) {
        // catch unique error
        if (err instanceof token.TokenVerifyException) {
            return Promise.reject(new Errors.InternalTokenError());
        }
        if (err instanceof token.TokenExpiredException) {
            return Promise.reject(new Errors.InternalTokenExpired());
        }
        return Promise.reject(err);
    }
}

export async function parseLiveStudioToken(internalToken: string): Promise<any> {
    try {
        return await TokenUtils.verifyLiveStudioToken(internalToken);
    } catch (err) {
        // catch unique error
        if (err instanceof token.TokenVerifyException) {
            return Promise.reject(new Errors.InternalTokenError());
        }
        if (err instanceof token.TokenExpiredException) {
            return Promise.reject(new Errors.InternalTokenExpired());
        }
        return Promise.reject(err);
    }
}

export async function parseBanWordsToken(internalToken: string): Promise<any> {
    try {
        return await TokenUtils.verifyBanWordsToken(internalToken);
    } catch (err) {
        // catch unique error
        if (err instanceof token.TokenVerifyException) {
            return Promise.reject(new Errors.InternalTokenError());
        }
        if (err instanceof token.TokenExpiredException) {
            return Promise.reject(new Errors.InternalTokenExpired());
        }
        return Promise.reject(err);
    }
}

/**
 * Verify twoFAToken token and find the main key entity according to it
 * @param twoFAToken
 */
export async function verifyAndParseTwoFAToken(twoFAToken: string): Promise<KeyEntityHolder & UserInfoHolder> {
    try {
        const decoded: TwoFATokenData = await TokenUtils.verifyTwoFAToken(twoFAToken);
        const entity: BaseEntity = await EntityCache.findOne({ id: decoded.entityId });
        if (!entity) {
            return Promise.reject(new Errors.EntityCouldNotBeFound());
        }
        return {
            keyEntity: entity,
            userId: decoded.userId,
            username: decoded.username,
        };
    } catch (err) {
        // catch unique error
        if (err instanceof token.TokenVerifyException) {
            return Promise.reject(new Errors.TwoFATokenError());
        }

        if (err instanceof token.TokenExpiredException) {
            return Promise.reject(new Errors.TwoFATokenExpired());
        }

        return Promise.reject(err);
    }
}

export async function verifyProviderSecret(providerUser: string,
                                           providerCode: string,
                                           providerSecret: string): Promise<GameProvider> {
    const provider: GameProvider = await GameProviderService.findOne({ user: providerUser, code: providerCode });

    if (provider.isSuspended()) {
        return Promise.reject(new Errors.GameProviderSuspendedError(providerCode));
    }

    if (providerSecret === provider.secret) {
        return provider;
    } else {
        return Promise.reject(new Errors.ProviderSecretIncorrect());
    }
}

export function validatePermissions(holder: SwaggerHolder & PermissionsHolder): boolean {
    const operationPermissions = extractPermissions(holder);
    if (!operationPermissions) {
        return false;
    }
    return isPermitted(operationPermissions, holder.permissions);
}

export function hasPermissions(holder: PermissionsHolder, permissions: string[]): boolean {
    return holder.isSuperAdmin || isPermitted(permissions, holder.permissions);
}

export async function getAvailableOperations(userPermissions: UserPermissions): Promise<Array<OperationDescription>> {
    const result = new Set<OperationDescription>();

    for (const p of userPermissions.grantedPermissions) {
        const array: Array<OperationDescription> = (await getOperationsInfo()).asMap.get(p);
        if (array) {
            for (const o of array) {
                if (isPermitted(o.permissions, userPermissions)) {
                    result.add(o);
                }
            }
        }
    }

    return Array.from(result);
}

/**
 * Filters user permissions based on entity settings for UI tab visibility
 */
export function filterPermissionsByEntitySettings(
    userPermissions: UserPermissions,
    entitySettings: any
): UserPermissions {
    if (!entitySettings) {
        return userPermissions;
    }

    const filteredPermissions = [...userPermissions.grantedPermissions];

    // If enableAnalyticsOnly is true, remove casino and engagement hub permissions
    if (entitySettings.enableAnalyticsOnly === true) {
        const filteredList = filteredPermissions.filter(permission =>
            !permission.includes("hub:casino") &&
            !permission.includes("hub:engagement")
        );
        return {
            ...userPermissions,
            grantedPermissions: filteredList
        };
    }

    // If disableCasinoHub is true, remove casino hub permissions
    if (entitySettings.disableCasinoHub === true) {
        const filteredList = filteredPermissions.filter(permission =>
            !permission.includes("hub:casino")
        );
        return {
            ...userPermissions,
            grantedPermissions: filteredList
        };
    }

    // If disableEngagementHub is true, remove engagement hub permissions
    if (entitySettings.disableEngagementHub === true) {
        const filteredList = filteredPermissions.filter(permission =>
            !permission.includes("hub:engagement")
        );
        return {
            ...userPermissions,
            grantedPermissions: filteredList
        };
    }

    return userPermissions;
}

export async function getOperations(permission?: string): Promise<Array<OperationDescription>> {
    const info: OperationsInfo = await getOperationsInfo();
    return permission ?
        info.asMap.get(permission) :
        info.asArray;
}

interface OperationsInfo {
    asMap: Map<string, Array<OperationDescription>>;
    asArray: Array<OperationDescription>;
}

function getSecurityPermissions(security): string[] {
    let securityPermissions: string[];
    for (let i = 0, length = security.length; i < length; i++) {
        if (security[i].hasOwnProperty(SCOPE_ID)) {
            securityPermissions = security[i][SCOPE_ID];
            break;
        }
    }
    return securityPermissions;
}

interface IMethodInfo {
    security: any[];
    description: string;
}
interface ISwagger {
    paths: {
        [path: string]: {
            [method: string]: IMethodInfo;
        }
    }
}
async function getOperationsInfo(): Promise<OperationsInfo> {
    if (!operations) {
        operations = {
            asMap: new Map(),
            asArray: [],
        };
        const swagger: ISwagger = await apiSwagger();
        for (const [path, methods] of Object.entries(swagger.paths)) {
            for (const [method, apiCall] of Object.entries(methods)) {
                if (apiCall.security) {
                    const scopePermissions: string[] = getSecurityPermissions(apiCall.security);
                    if (scopePermissions) {
                        const description: OperationDescription = {
                            id: method.toString().toUpperCase() + " " + path,
                            description: apiCall.description,
                            permissions: scopePermissions,
                        };

                        // Argument of type ''
                        // {
                        //     swagger: {
                        //         operation: {
                        //             security: {
                        //                 "Permissions": string[];
                        //             }[];
                        //         };
                        //     };
                        //     permissions: {
                        //         grantedPermissions: string[];
                        //     };
                        // }
                        // is not assignable to parameter of type 'SwaggerHolder & PermissionsHolder'.
                        for (const permission of scopePermissions) {
                            let descriptions: OperationDescription[] = operations.asMap.get(permission);
                            if (!descriptions) {
                                descriptions = [];
                                operations.asMap.set(permission, descriptions);
                            }

                            descriptions.push(description);
                        }
                        operations.asArray.push(description);
                    }
                }
            }
        }
    }
    return operations;
}

export function extractPermissions(request: any): Array<string> {
    if (!request.swagger || !request.swagger.operation || !request.swagger.operation.security) {
        return undefined;
    }

    return getSecurityPermissions(request.swagger.operation.security);
}

export function isPermitted(operationPermissions: Array<string>, userPermissions: UserPermissions): boolean {
    for (const p of operationPermissions) {
        if (userPermissions.grantedPermissions.indexOf(p) >= 0) {
            return true;
        }
    }

    return false;
}

export function buildKey(...values: string[]): string {
    const key = values.filter(Boolean).join(":");
    return `${KEY_BASE}:${key}`;
}

/**
 * Generates password reset token. Uses db to ensure user has only one valid token at a time.
 */
export async function createResetToken(entityKey: string,
                                       username: string,
                                       tokenPrefixKey: string): Promise<string> {
    const resetToken: string = crypto.randomBytes(64).toString(config.resetToken.outputEncoding as BufferEncoding);
    const tokenKey: string = buildKey(tokenPrefixKey, entityKey, username);

    return stashValueForToken(tokenKey, resetToken);
}

/**
 * Verifies password reset token and invalidates it by removing from db.
 */
export async function verifyResetToken(entityKey: string,
                                       username: string,
                                       tokenPrefixKey: string,
                                       resetToken: string,
                                       reason?: string): Promise<void> {
    const tokenKey: string = buildKey(tokenPrefixKey, entityKey, username);

    const currentToken = await unstashValueForToken(tokenKey, reason);
    if (!currentToken || resetToken !== currentToken) {
        return Promise.reject(new Errors.AccessTokenExpired(reason));
    }
}

const DIGITS = "123456789";

/**
 * Generates second auth step's code. Uses db to ensure user has only one valid token at a time.
 */
export async function createTwoFACode(entityKey: string,
                                      username: string,
                                      tokenPrefixKey: string = "secondStepAuth"): Promise<string> {
    let code: string = "";
    for (let i = 0; i < OTP_LENGTH; i++) {
        code += DIGITS[DIGITS.length * Math.random() | 0];
    }

    const tokenKey: string = buildKey(tokenPrefixKey, entityKey, username);

    return stashValueForToken(tokenKey, code, config.twoFAtoken.expiresIn);
}

/**
 * Generates expiring marker that will be used for checking if auth code is sent too frequently
 */
export async function createAuthCodeSentMarker(
    entityKey: string,
    username: string,
    userAuthType: SECURITY_AUTH_TYPE): Promise<string> {

    const tokenPrefixKey: string = "authCodeSentMarker";
    const tokenKey: string = buildKey(tokenPrefixKey, entityKey, username, userAuthType);

    return stashValueForToken<string>(tokenKey, "ignore", config.twoFAtoken.smsEmailSendingInterval);
}

/**
 * Verifies if auth code has not been recently
 */
export async function verifyAuthCodeSentMarker(entityKey: string,
                                               username: string,
                                               userAuthType: SECURITY_AUTH_TYPE): Promise<void> {
    const tokenKey: string = buildKey("authCodeSentMarker", entityKey, username, userAuthType);

    let value;
    try {
        value = await getValueForToken(tokenKey);
    } catch (err) { /* do nothing */
    }
    if (value) {
        return Promise.reject(new Errors.TwoFAAuthCodeHasBeenSentRecently());
    }
}

/**
 * Verifies second auth step's code.
 */
export async function verifyTwoFACode(entityKey: string,
                                      username: string,
                                      code: string,
                                      tokenPrefixKey: string = "secondStepAuth"): Promise<void> {
    const tokenKey: string = buildKey(tokenPrefixKey, entityKey, username);

    let currentCode;
    try {
        currentCode = await unstashValueForToken(tokenKey);
    } catch (err) { /* do nothing */
    }
    if (!currentCode) {
        return Promise.reject(new Errors.TwoFAAuthCodeExpired());
    }
    if (code !== currentCode) {
        return Promise.reject(new Errors.AuthCodeIsIncorrect());
    }
}

/**
 * Generates secret for user's google authenticator. Uses db to ensure user has only one valid secret at a time.
 */
export async function createGASecret(entityKey: string,
                                     username: string,
                                     tokenPrefixKey: string = "gaSecretAuth"): Promise<any> {
    const formattedSecretKey = authenticator.generateKey();

    const tokenStashKey: string = buildKey(tokenPrefixKey, entityKey, username);

    await stashValueForToken(tokenStashKey, formattedSecretKey, config.twoFAtoken.expiresIn);
    return formattedSecretKey;
}

export async function createGATotpUri(key: string, userEmail: string): Promise<string> {
    const uri = authenticator.generateTotpUri(key, userEmail, "Skywind", "SHA1", OTP_LENGTH, 60);
    return QRCode.toDataURL(uri);
}

/**
 * Verifies google authenticator's code prior to setting user's twoFAType to google.
 */
export async function verifyGASelection(entityKey: string,
                                        username: string,
                                        code: string,
                                        tokenPrefixKey: string = "gaSecretAuth"): Promise<string> {
    const tokenStashKey: string = buildKey(tokenPrefixKey, entityKey, username);

    let formattedKey;
    try {
        formattedKey = await unstashValueForToken(tokenStashKey);
    } catch (err) { /* do nothing */
    }
    if (!formattedKey) {
        return Promise.reject(new Errors.TwoFAAuthCodeExpired());
    }

    const verified = authenticator.verifyToken(formattedKey, code);

    if (!verified) {
        return Promise.reject(new Errors.AuthCodeIsIncorrect());
    }

    return formattedKey as string;
}

/**
 * Verifies google authenticator's code.
 */
export async function verifyGACode(base32secret: string,
                                   code: string): Promise<void> {

    const verified = authenticator.verifyToken(base32secret, code);

    if (!verified) {
        return Promise.reject(new Errors.AuthCodeIsIncorrect());
    }
}

export async function stashValueForToken<T extends string>(key: string,
                                            data: T,
                                            expiresIn: number = config.resetToken.expiresIn): Promise<T> {
    return redis.usingDb<T>(async (db) => {
        await db.setex(key, expiresIn, data);
        return data;
    });
}

export async function unstashValueForToken(key: string, reason?: string): Promise<string> {
    return redis.usingDb(async (db) => {
        const value = await db.get(key);
        if (!value) {
            return Promise.reject(new Errors.AccessTokenExpired(reason));
        }

        await db.del(key);
        return value;
    });
}

export async function getValueForToken(key: string): Promise<string> {
    return redis.usingDb(async (db) => {
        return db.get(key);
    });
}

export function createSalt(): Promise<string> {
    return randomBytes(64)
        .then(buf => {
            return buf.toString("hex");
        });
}

export function encryptPassword(salt: string, password: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
        crypto.pbkdf2(password, salt, 1000, 96, "sha512", function (err, key) {
            if (err) {
                return reject(err);
            }
            return resolve(key.toString("hex"));
        });
    });
}

export async function getFailedAttempts(entityKey: string, name: string,
                                        eventType: SECURITY_EVENT_TYPE = SECURITY_EVENT_TYPE.LOGIN): Promise<string> {
    const prefix = getEventKeysParts(eventType).failedPfx;
    return redis.usingDb<string>(async (db) => {
        const key = buildKey(prefix, entityKey, name);
        return await db.get(key);
    });
}

export async function checkBlocked(entityKey: string, username: string): Promise<boolean> {
    return redis.usingDb<boolean>(async (db) => {
        const expiresMinutesKey = buildKey("expiresFailedLogins", entityKey, username);
        return !!await db.get(expiresMinutesKey);
    });
}

export async function checkBlockedAuthentication(
    entityKey: string,
    username: string,
    personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.USER): Promise<void> {

    return redis.usingDb<void>(async (db) => {
        const key = buildKey("failedLogins", entityKey, username);
        const expiresMinutesKey = buildKey("expiresFailedLogins", entityKey, username);
        const [isBlocked, value] = await db.mget(expiresMinutesKey, key);
        if (!!isBlocked) {
            return Promise.reject(new Errors.UserAuthenticationBlocked(personType));
        }

        const loginAttempts = loginConfigMapping[personType][`${personType}LoginAttempts`];
        if (value && +value >= loginAttempts) {
            await incrementBlockingTimeout(db, expiresMinutesKey, personType);
            measureProvider.incrementGauge(`blocked_login_${personType}`, 1, username);
            return Promise.reject(new Errors.UserAuthenticationBlocked(personType));
        }
    });
}

export async function checkUserBlockedByLoginAttempts(entityKey: string, username: string): Promise<boolean> {
   return redis.usingDb<boolean>(async (db) => {
        const failedLoginsKey = buildKey("failedLogins", entityKey, username);
        const failedLoginsCounter = await db.get(failedLoginsKey);
        const loginAttempts = loginConfigMapping.user.userLoginAttempts;
        if (failedLoginsCounter && +failedLoginsCounter >= loginAttempts) {
            return true;
        }
    });
}

export function checkUserBlocked(user: UserModel): void {
    const status = user.status;
    if (status === UserStatus.LOCKED_BY_AUTH) {
        throw new Errors.UserAuthenticationBlocked(SECURITY_AUTH_TYPE.USER);
    }
}

export async function blockUser(user: UserModel): Promise<void> {
    const userName = user.username;
    measureProvider.incrementGauge(`blocked_login_${SECURITY_AUTH_TYPE.USER}`, 1, userName);
    await user.update({ status: UserStatus.LOCKED_BY_AUTH });
}

export async function unblockUser(entityKey: string, user: UserModel): Promise<void> {
    const status = user.status;
    if (status !== UserStatus.LOCKED_BY_AUTH) {
        return Promise.reject(new Errors.ClearBlockedAuthenticationFails(SECURITY_AUTH_TYPE.USER));
    }
    const userName = user.get("username");
    const keyForLoginFails = buildKey("failedLogins", entityKey, userName);
    await redis.usingDb<void>(async (db) => {
        await db.del(keyForLoginFails);
    });

    await user.update({ status: UserStatus.NORMAL });
}

export async function clearBlockedAuthentication(
    entityKey: string,
    username: string,
    personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.USER): Promise<void> {

    return redis.usingDb(async (db) => {
        const keyForLoginFails = buildKey("failedLogins", entityKey, username);
        const key = buildKey("expiresFailedLogins", entityKey, username);

        const isBlocked = await db.get(key);
        if (!isBlocked) {
            return Promise.reject(new Errors.ClearBlockedAuthenticationFails(personType));
        }
        await db.del(keyForLoginFails, key);
    });
}

export async function clearBlockedChangingPassword(
    entityKey: string,
    username: string,
    personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.USER): Promise<void> {

    return redis.usingDb(async (db) => {
        const keyForChangePasswordFails = buildKey("changePasswordAttempts", entityKey, username);
        const expiresMinutesKey = buildKey("expiresChangePasswordAttempts", entityKey, username);

        const failsCount = await db.get(keyForChangePasswordFails);

        if (!failsCount) {
            return Promise.reject(new Errors.ClearBlockedChangingPasswordFails(personType));
        }
        await db.del(keyForChangePasswordFails, expiresMinutesKey);
    });
}

async function incrementBlockingTimeout(
    db: RedisClient,
    key: string,
    personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.USER) {

    const resetBlockingTimeout = loginConfigMapping[personType][`${personType}ResetBlockingTimeout`];
    await db.multi()
        .incr(key)
        .expire(key, resetBlockingTimeout)
        .exec();
}

export async function incrementFailures(
    entityKey: string,
    name: string,
    personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.USER,
    eventType: SECURITY_EVENT_TYPE = SECURITY_EVENT_TYPE.LOGIN,
    expiresIn?: number): Promise<void> {

    return redis.usingDb<void>(async (db) => {
        const prxSfx = getEventKeysParts(eventType);
        const key = buildKey(prxSfx.failedPfx, entityKey, name);
        const expiresMinutesKey = buildKey(prxSfx.expiresPfx, entityKey, name);

        const [expiresMinutesValue = 0, attemptsCount = 0] = await db.mget(expiresMinutesKey, key);
        const expiresValue = (+expiresMinutesValue + 1) * SECONDS_IN_MINUTE;

        const personLoginConfig = loginConfigMapping[personType];
        const loginAttempts = personLoginConfig[`${personType}${prxSfx.attemptsSfx}`];

        expiresIn = expiresIn || personLoginConfig[`${personType}IdleTimeout`];

        if (attemptsCount >= loginAttempts) {
            await incrementBlockingTimeout(db, expiresMinutesKey, personType);
        }
        const expires = Math.min(expiresValue, expiresIn);

        await db.multi().incr(key).expire(key, expires).exec();
    });
}

export async function incrementFailedLoginsCountAndBlockUserIfRequired(entityKey: string,
                                                                       user: UserModel): Promise<void> {
    return redis.usingDb<void>(async (db) => {
        const userName = user.username;
        const failedLoginsCounterKey = buildKey("failedLogins", entityKey, userName);
        const failedLoginAttempts = +await db.get(failedLoginsCounterKey) || 0;
        const allowedLoginAttempts = config.settingsDefaults.userLoginAttempts;

        if (failedLoginAttempts >= allowedLoginAttempts) {
            await blockUser(user);
        }
        const failedLoginKeyExpiration = config.settingsDefaults.userIdleTimeout;
        await db.multi().incr(failedLoginsCounterKey).expire(failedLoginsCounterKey, failedLoginKeyExpiration).exec();
    });
}

export async function checkBlockedPasswordChange(entityKey: string,
                                                 username: string,
                                                 personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.USER):
    Promise<void> {
    return redis.usingDb<any>(async (db) => {
        const changePasswordAttemptsForPerson = loginConfigMapping[personType][`${personType}ChangePasswordAttempts`];
        const key = buildKey("changePasswordAttempts", entityKey, username);
        return db.get(key).then((value) => {
            if (value && +value >= changePasswordAttemptsForPerson) {
                log.warn(`Password change was blocked - ${personType} ${username}`);
                measureProvider.incrementGauge(`blocked_password_change_${personType}`, 1, username);
                return Promise.reject(new Errors.ChangePasswordBlocked(personType));
            }
        });
    });
}

export async function incrementPasswordChangeFail(entityKey: string, username: string,
                                                  personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.USER,
                                                  expiresIn?: number): Promise<void> {
    return redis.usingDb<void>(async (db) => {
        const key = buildKey("changePasswordAttempts", entityKey, username);
        const expiresKey = buildKey("expiresChangePasswordAttempts", entityKey, username);
        const expiresMinutesValue: number = +await db.get(expiresKey) || 0;
        const attemptsCount: number = +await db.get(key) || 0;
        const expiresValue = (expiresMinutesValue + 1) * SECONDS_IN_MINUTE;

        const personLoginConfig = loginConfigMapping[personType];
        const changePasswordAttempts = personLoginConfig[`${personType}ChangePasswordAttempts`];
        const resetBlockingTimeout = personLoginConfig[`${personType}ResetBlockingTimeout`];
        expiresIn = expiresIn || personLoginConfig[`${personType}IdleTimeout`];

        if (attemptsCount >= changePasswordAttempts) {
            await db.multi().incr(expiresKey).expire(expiresKey, resetBlockingTimeout).exec();
        }
        const expires = Math.min(expiresValue, expiresIn);

        await db.multi().incr(key).expire(key, expires).exec();
    });
}

export async function checkPasswordResets(ip: string): Promise<boolean> {
    return redis.usingDb<boolean>(async (db) => {
        const key = buildKey("passwordResets", ip);
        const resets = await db.get(key);
        const allowedResets = loginConfigMapping[SECURITY_AUTH_TYPE.USER][`${SECURITY_AUTH_TYPE.USER}PwdResetAttempts`];
        return (!resets || +resets < allowedResets);
    });
}

export async function incrementPasswordResets(ip: string): Promise<void> {
    return redis.usingDb<void>(async (db) => {
        const key = buildKey("passwordResets", ip);
        await incrementBlockingTimeout(db, key, SECURITY_AUTH_TYPE.USER);
    });
}

export async function clearBlockedResetPassword(ip: string): Promise<void> {
    return redis.usingDb<void>(async (db) => {
        const key = buildKey("passwordResets", ip);
        const resets = await db.get(key);
        if (resets) {
            await db.del(key);
        }
    });
}

export async function createSaltAndPassword(password: string): Promise<{ salt: string, password: string }> {
    const salt: string = await createSalt();
    const pwd: string = await encryptPassword(salt, password);
    return { salt, password: pwd };
}

export async function addCaptchaToken(entityKey: string,
                                      username: string,
                                      captchaToken: string,
                                      personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.PLAYER): Promise<void> {
    return redis.usingDb<void>(async (db) => {
        const key = buildKey(`${personType}CaptchaShow`, entityKey, username);
        const seconds = loginConfigMapping[personType][`${personType}CaptchaKeepAlive`];
        await db.setex(key, seconds, captchaToken);
    });
}

export async function getCaptchaToken(entityKey: string,
                                      username: string,
                                      personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.PLAYER): Promise<string> {
    return redis.usingDb<string>(async (db) => {
        const key = buildKey(`${personType}CaptchaShow`, entityKey, username);
        return db.get(key);
    });
}

export async function removeCaptchaToken(entityKey: string,
                                         username: string,
                                         personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.PLAYER): Promise<number> {
    return redis.usingDb<number>(async (db) => {
        const key = buildKey(`${personType}CaptchaShow`, entityKey, username);
        return db.del(key);
    });
}

export async function generateCaptcha(
    entityKey: string,
    name: string,
    personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.PLAYER): Promise<CaptchaInfo> {

    const captchaData = await captcha();
    const key = getCaptchaKey(entityKey, name, captchaData.token);
    await addCaptchaToken(entityKey, name, key, personType);
    const encryptedToken = await encryptCaptchaToken(key, personType);
    return { csrfToken: encryptedToken, image : "data:image/gif;base64," + captchaData.buffer.toString("base64") };
}

export function checkCaptcha(
    attemptsFailedLogin: number,
    personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.PLAYER,
    eventType: SECURITY_EVENT_TYPE = SECURITY_EVENT_TYPE.LOGIN): boolean {

    const suffix = getEventKeysParts(eventType).attemptsSfx;
    const loginAttempts = loginConfigMapping[personType][`${personType}${suffix}`];
    const whenCaptchaShows = loginConfigMapping[personType][`${personType}${suffix}ShowCaptcha`];
    if (attemptsFailedLogin >= whenCaptchaShows) {
        return true;
    }
    return attemptsFailedLogin >= loginAttempts;
}

export function getCaptchaKey(entityKey: string, username: string, captchaToken: string) {
    return `${entityKey}:${username}:${captchaToken}`;
}

export async function encryptCaptchaToken(text: string,
                                          personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.PLAYER): Promise<string> {
    const masterKey = getMasterKey(personType);
    return new Promise<string>((resolve, reject) => {
        try {
            const iv = crypto.randomBytes(12);
            const salt = crypto.randomBytes(64);
            const key = crypto.pbkdf2Sync(masterKey, salt, 2145, 32, "sha512");
            const cipher = crypto.createCipheriv("aes-256-gcm", key, iv);
            const encrypted = Buffer.concat([cipher.update(text, "utf8"), cipher.final()]);
            const tag = cipher.getAuthTag();
            return resolve(Buffer.concat([salt, iv, tag, encrypted]).toString("base64"));
        } catch (err) {
            return reject(err);
        }
    });
}

export async function decryptCaptchaToken(data: string,
                                          personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.PLAYER): Promise<string> {
    const masterKey = getMasterKey(personType);
    return new Promise<string>((resolve, reject) => {
        try {
            const bData = Buffer.from(data, "base64");
            const salt = bData.slice(0, 64);
            const iv = bData.slice(64, 76);
            const tag = bData.slice(76, 92);
            const text = bData.slice(92);
            const key = crypto.pbkdf2Sync(masterKey, salt, 2145, 32, "sha512");
            const decipher = crypto.createDecipheriv("aes-256-gcm", key, iv);
            decipher.setAuthTag(tag);

            return resolve(decipher.update(text, undefined, "utf8") + decipher.final("utf8"));
        } catch (err) {
            return reject(err);
        }
    });
}

function getMasterKey(personType: SECURITY_AUTH_TYPE) {
    return loginConfigMapping[personType][`${personType}CaptchaMasterKey`];
}

export function isCaptchaEnabled(personType: SECURITY_AUTH_TYPE) {
    return loginConfigMapping[personType][`${personType}CaptchaEnable`];
}

export async function checkAndRemoveCaptcha(entityKey: string,
                                            code: string,
                                            personType: SECURITY_AUTH_TYPE = SECURITY_AUTH_TYPE.PLAYER,
                                            eventType: SECURITY_EVENT_TYPE = SECURITY_EVENT_TYPE.LOGIN,
                                            captchaToken?: string,
                                            csrfToken?: string): Promise<void> {
    const storedCaptcha = await getCaptchaToken(entityKey, code, personType);
    if (storedCaptcha) {
        if (!captchaToken || !csrfToken) {
            return Promise.reject(
                new Errors.CaptchaParamsDontExist(await generateCaptcha(entityKey, code))
            );
        }
        const decrypted = await decryptCaptchaToken(csrfToken).catch(async () => {
            return Promise.reject(
                new Errors.CaptchaParamsNotValid(await generateCaptcha(entityKey, code))
            );
        });
        const key = getCaptchaKey(entityKey, code, captchaToken);
        if (key === decrypted && key === storedCaptcha) {
            await removeCaptchaToken(entityKey, code);
            return;
        }
        await incrementFailures(entityKey, code, personType, eventType);
        return Promise.reject(
            new Errors.CaptchaDoesntMatch(await generateCaptcha(entityKey, code, personType))
        );
    }
}
