import { NextFunction, Request, Response, Router } from "express";
import { authenticate, authorize } from "./middleware/middleware";
import { getAvailableOperations, getOperations, PermissionsHolder, filterPermissionsByEntitySettings, KeyEntityHolder } from "../services/security";
import { getPermissionsDescriptions } from "../services/permission";
import { getEntitySettings } from "../services/settings";

const router: Router = Router();

router.get("/permissions",
    authenticate,
    async(req: Request, res: Response, next: NextFunction) => {
        res.send(await getPermissionsDescriptions());
    });

// NOTE: API returns only operations from management-api and doesn't include anything from other components
// TODO: Think about method usability
router.get("/operations",
    authenticate,
    async(req: Request, res: Response, next: NextFunction) => {
        res.send(await getOperations(req.query["permission"]));
    });

// NOTE: API returns only operations from management-api and doesn't include anything from other components
// TODO: Think about method usability
router.get("/availableOperations",
    authenticate,
    async(req: Request&PermissionsHolder, res: Response, next: NextFunction) => {
        res.send(await getAvailableOperations(req.permissions));
    });

// Get filtered permissions based on entity settings for UI tab visibility
router.get("/entities/:path/filteredPermissions",
    authenticate,
    authorize,
    async(req: Request & PermissionsHolder & KeyEntityHolder, res: Response, next: NextFunction) => {
        try {
            const entityPath = req.params.path;
            const entitySettings = await getEntitySettings(entityPath);
            const filteredPermissions = filterPermissionsByEntitySettings(req.permissions, entitySettings);
            res.send(filteredPermissions);
            next();
        } catch (err) {
            next(err);
        }
    });

export default router;
