import { expect, should, use } from "chai";
import { createComplexStructure, truncate, withTransaction } from "../entities/helper";
import EntitySettingsService from "../../skywind/services/settings";
import { filterPermissionsByEntitySettings } from "../../skywind/services/security";
import { UserPermissions } from "../../skywind/entities/user";
import { EntitySettings } from "../../skywind/entities/settings";
import * as Errors from "../../skywind/errors";
import getEntityFactory from "../../skywind/services/entityFactory";
import { BaseEntity } from "../../skywind/entities/entity";

const chaiAsPromise = require("chai-as-promised");
should();
use(chaiAsPromise);

describe("UI Tab Visibility Settings", () => {
    let master: BaseEntity;
    let entity: BaseEntity;

    beforeEach(async () => {
        await truncate();
        master = await createComplexStructure();
        entity = await getEntityFactory(master).createEntity({
            name: "test-entity",
            description: "Test entity for UI tab visibility"
        });
    });

    describe("Entity Settings Validation", () => {
        it("should accept valid boolean values for UI tab settings", async () => {
            const settingsService = new EntitySettingsService(entity);
            const settings = {
                disableCasinoHub: true,
                disableEngagementHub: false,
                enableAnalyticsOnly: true
            };

            const result = await settingsService.patch(settings);
            expect(result.disableCasinoHub).to.equal(true);
            expect(result.disableEngagementHub).to.equal(false);
            expect(result.enableAnalyticsOnly).to.equal(true);
        });

        it("should reject non-boolean values for disableCasinoHub", async () => {
            const settingsService = new EntitySettingsService(entity);
            const settings = {
                disableCasinoHub: "invalid" as any
            };

            await settingsService.patch(settings)
                .should.eventually.be.rejectedWith(Errors.ValidationError);
        });

        it("should reject non-boolean values for disableEngagementHub", async () => {
            const settingsService = new EntitySettingsService(entity);
            const settings = {
                disableEngagementHub: "invalid" as any
            };

            await settingsService.patch(settings)
                .should.eventually.be.rejectedWith(Errors.ValidationError);
        });

        it("should reject non-boolean values for enableAnalyticsOnly", async () => {
            const settingsService = new EntitySettingsService(entity);
            const settings = {
                enableAnalyticsOnly: "invalid" as any
            };

            await settingsService.patch(settings)
                .should.eventually.be.rejectedWith(Errors.ValidationError);
        });

        it("should reject conflicting settings when enableAnalyticsOnly is true", async () => {
            const settingsService = new EntitySettingsService(entity);
            const settings = {
                enableAnalyticsOnly: true,
                disableCasinoHub: false
            };

            await settingsService.patch(settings)
                .should.eventually.be.rejectedWith(Errors.ValidationError);
        });
    });

    describe("Permission Filtering", () => {
        const mockUserPermissions: UserPermissions = {
            grantedPermissions: [
                "hub:casino",
                "hub:analytics", 
                "hub:engagement",
                "keyentity:bi:reports",
                "keyentity:bi:report:player",
                "other:permission"
            ]
        };

        it("should not filter permissions when no UI settings are configured", () => {
            const entitySettings = {};
            const filtered = filterPermissionsByEntitySettings(mockUserPermissions, entitySettings);
            
            expect(filtered.grantedPermissions).to.deep.equal(mockUserPermissions.grantedPermissions);
        });

        it("should filter casino hub permissions when disableCasinoHub is true", () => {
            const entitySettings = { disableCasinoHub: true };
            const filtered = filterPermissionsByEntitySettings(mockUserPermissions, entitySettings);
            
            expect(filtered.grantedPermissions).to.not.include("hub:casino");
            expect(filtered.grantedPermissions).to.include("hub:analytics");
            expect(filtered.grantedPermissions).to.include("hub:engagement");
            expect(filtered.grantedPermissions).to.include("other:permission");
        });

        it("should filter engagement hub permissions when disableEngagementHub is true", () => {
            const entitySettings = { disableEngagementHub: true };
            const filtered = filterPermissionsByEntitySettings(mockUserPermissions, entitySettings);
            
            expect(filtered.grantedPermissions).to.include("hub:casino");
            expect(filtered.grantedPermissions).to.include("hub:analytics");
            expect(filtered.grantedPermissions).to.not.include("hub:engagement");
            expect(filtered.grantedPermissions).to.include("other:permission");
        });

        it("should filter both casino and engagement hub permissions when enableAnalyticsOnly is true", () => {
            const entitySettings = { enableAnalyticsOnly: true };
            const filtered = filterPermissionsByEntitySettings(mockUserPermissions, entitySettings);
            
            expect(filtered.grantedPermissions).to.not.include("hub:casino");
            expect(filtered.grantedPermissions).to.include("hub:analytics");
            expect(filtered.grantedPermissions).to.not.include("hub:engagement");
            expect(filtered.grantedPermissions).to.include("keyentity:bi:reports");
            expect(filtered.grantedPermissions).to.include("other:permission");
        });

        it("should preserve analytics and BI permissions when enableAnalyticsOnly is true", () => {
            const entitySettings = { enableAnalyticsOnly: true };
            const filtered = filterPermissionsByEntitySettings(mockUserPermissions, entitySettings);
            
            expect(filtered.grantedPermissions).to.include("hub:analytics");
            expect(filtered.grantedPermissions).to.include("keyentity:bi:reports");
            expect(filtered.grantedPermissions).to.include("keyentity:bi:report:player");
        });

        it("should handle null/undefined entity settings gracefully", () => {
            const filtered1 = filterPermissionsByEntitySettings(mockUserPermissions, null);
            const filtered2 = filterPermissionsByEntitySettings(mockUserPermissions, undefined);
            
            expect(filtered1.grantedPermissions).to.deep.equal(mockUserPermissions.grantedPermissions);
            expect(filtered2.grantedPermissions).to.deep.equal(mockUserPermissions.grantedPermissions);
        });

        it("should preserve other user permission properties", () => {
            const userPermissionsWithExtra = {
                ...mockUserPermissions,
                userId: 123,
                username: "testuser"
            } as any;
            
            const entitySettings = { disableCasinoHub: true };
            const filtered = filterPermissionsByEntitySettings(userPermissionsWithExtra, entitySettings);
            
            expect(filtered.userId).to.equal(123);
            expect(filtered.username).to.equal("testuser");
        });
    });

    describe("Settings Integration", () => {
        it("should persist and retrieve UI tab visibility settings", async () => {
            const settingsService = new EntitySettingsService(entity);
            
            // Set the settings
            await settingsService.patch({
                disableCasinoHub: true,
                disableEngagementHub: false,
                enableAnalyticsOnly: false
            });

            // Retrieve and verify
            const retrievedSettings = await settingsService.get();
            expect(retrievedSettings.disableCasinoHub).to.equal(true);
            expect(retrievedSettings.disableEngagementHub).to.equal(false);
            expect(retrievedSettings.enableAnalyticsOnly).to.equal(false);
        });

        it("should handle partial updates of UI tab settings", async () => {
            const settingsService = new EntitySettingsService(entity);
            
            // Set initial settings
            await settingsService.patch({
                disableCasinoHub: true,
                disableEngagementHub: true
            });

            // Update only one setting
            await settingsService.patch({
                enableAnalyticsOnly: true
            });

            // Verify all settings
            const retrievedSettings = await settingsService.get();
            expect(retrievedSettings.disableCasinoHub).to.equal(true);
            expect(retrievedSettings.disableEngagementHub).to.equal(true);
            expect(retrievedSettings.enableAnalyticsOnly).to.equal(true);
        });
    });
});
