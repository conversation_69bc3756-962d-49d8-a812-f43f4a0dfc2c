import { suite, test, timeout } from "mocha-typescript";
import { expect } from "chai";
import { Application } from "express";
import { BaseEntity } from "../../skywind/entities/entity";
import getEntityFactory from "../../skywind/services/entityFactory";
import { application } from "../../skywind/server";
import { createComplexStructure, createDefaultMerchantTypes, truncate } from "../entities/helper";
import * as RoleService from "../../skywind/services/role";
import { default as getUserService, initDB, UserService } from "../../skywind/services/user/user";
import { UserInfo } from "../../skywind/entities/user";
import EntitySettingsService from "../../skywind/services/settings";

const request = require("supertest");

@suite("Filtered Permissions API", timeout(20000))
class FilteredPermissionsSpec {
    public static server: Application;
    public static master: BaseEntity;
    public static entity: BaseEntity;
    public static userService: UserService;
    public static token: string;
    public static user: UserInfo;

    public static async before() {
        FilteredPermissionsSpec.server = await application.get();

        await truncate();

        FilteredPermissionsSpec.master = await createComplexStructure();
        await initDB();
        await createDefaultMerchantTypes();

        FilteredPermissionsSpec.entity = await getEntityFactory(FilteredPermissionsSpec.master).createEntity({
            name: "test-entity",
            description: "Test entity for filtered permissions"
        });

        FilteredPermissionsSpec.userService = getUserService(FilteredPermissionsSpec.master);

        // Create a role with hub permissions
        const role = await RoleService.createRole(FilteredPermissionsSpec.master, {
            name: "TestRole",
            description: "Test role with hub permissions",
            permissions: [
                "hub:casino",
                "hub:analytics", 
                "hub:engagement",
                "keyentity:bi:reports",
                "keyentity:bi:report:player",
                "other:permission"
            ]
        });

        // Create a user with the role
        FilteredPermissionsSpec.user = await FilteredPermissionsSpec.userService.createUser({
            username: "testuser",
            password: "password123",
            email: "<EMAIL>",
            roles: [role.name]
        });

        // Get access token
        const loginResponse = await request(FilteredPermissionsSpec.server)
            .post("/v1/login")
            .send({
                username: "testuser",
                password: "password123"
            });

        FilteredPermissionsSpec.token = loginResponse.body.accessToken;
    }

    @test()
    public async "should return filtered permissions when disableCasinoHub is true"() {
        // Set entity settings to disable casino hub
        const settingsService = new EntitySettingsService(FilteredPermissionsSpec.entity);
        await settingsService.patch({
            disableCasinoHub: true
        });

        const response = await request(FilteredPermissionsSpec.server)
            .get(`/v1/entities/${FilteredPermissionsSpec.entity.path}/filteredPermissions`)
            .set("Authorization", `Bearer ${FilteredPermissionsSpec.token}`)
            .expect(200);

        expect(response.body.grantedPermissions).to.not.include("hub:casino");
        expect(response.body.grantedPermissions).to.include("hub:analytics");
        expect(response.body.grantedPermissions).to.include("hub:engagement");
        expect(response.body.grantedPermissions).to.include("other:permission");
    }

    @test()
    public async "should return filtered permissions when disableEngagementHub is true"() {
        // Set entity settings to disable engagement hub
        const settingsService = new EntitySettingsService(FilteredPermissionsSpec.entity);
        await settingsService.patch({
            disableEngagementHub: true
        });

        const response = await request(FilteredPermissionsSpec.server)
            .get(`/v1/entities/${FilteredPermissionsSpec.entity.path}/filteredPermissions`)
            .set("Authorization", `Bearer ${FilteredPermissionsSpec.token}`)
            .expect(200);

        expect(response.body.grantedPermissions).to.include("hub:casino");
        expect(response.body.grantedPermissions).to.include("hub:analytics");
        expect(response.body.grantedPermissions).to.not.include("hub:engagement");
        expect(response.body.grantedPermissions).to.include("other:permission");
    }

    @test()
    public async "should return only analytics permissions when enableAnalyticsOnly is true"() {
        // Set entity settings to enable analytics only
        const settingsService = new EntitySettingsService(FilteredPermissionsSpec.entity);
        await settingsService.patch({
            enableAnalyticsOnly: true
        });

        const response = await request(FilteredPermissionsSpec.server)
            .get(`/v1/entities/${FilteredPermissionsSpec.entity.path}/filteredPermissions`)
            .set("Authorization", `Bearer ${FilteredPermissionsSpec.token}`)
            .expect(200);

        expect(response.body.grantedPermissions).to.not.include("hub:casino");
        expect(response.body.grantedPermissions).to.include("hub:analytics");
        expect(response.body.grantedPermissions).to.not.include("hub:engagement");
        expect(response.body.grantedPermissions).to.include("keyentity:bi:reports");
        expect(response.body.grantedPermissions).to.include("keyentity:bi:report:player");
        expect(response.body.grantedPermissions).to.include("other:permission");
    }

    @test()
    public async "should return all permissions when no UI restrictions are set"() {
        // Reset entity settings (no UI restrictions)
        const settingsService = new EntitySettingsService(FilteredPermissionsSpec.entity);
        await settingsService.patch({
            disableCasinoHub: false,
            disableEngagementHub: false,
            enableAnalyticsOnly: false
        });

        const response = await request(FilteredPermissionsSpec.server)
            .get(`/v1/entities/${FilteredPermissionsSpec.entity.path}/filteredPermissions`)
            .set("Authorization", `Bearer ${FilteredPermissionsSpec.token}`)
            .expect(200);

        expect(response.body.grantedPermissions).to.include("hub:casino");
        expect(response.body.grantedPermissions).to.include("hub:analytics");
        expect(response.body.grantedPermissions).to.include("hub:engagement");
        expect(response.body.grantedPermissions).to.include("keyentity:bi:reports");
        expect(response.body.grantedPermissions).to.include("other:permission");
    }

    @test()
    public async "should require authentication"() {
        await request(FilteredPermissionsSpec.server)
            .get(`/v1/entities/${FilteredPermissionsSpec.entity.path}/filteredPermissions`)
            .expect(401);
    }

    @test()
    public async "should handle non-existent entity path"() {
        await request(FilteredPermissionsSpec.server)
            .get("/v1/entities/non-existent-path/filteredPermissions")
            .set("Authorization", `Bearer ${FilteredPermissionsSpec.token}`)
            .expect(404);
    }

    @test()
    public async "should preserve user permission structure"() {
        const settingsService = new EntitySettingsService(FilteredPermissionsSpec.entity);
        await settingsService.patch({
            disableCasinoHub: true
        });

        const response = await request(FilteredPermissionsSpec.server)
            .get(`/v1/entities/${FilteredPermissionsSpec.entity.path}/filteredPermissions`)
            .set("Authorization", `Bearer ${FilteredPermissionsSpec.token}`)
            .expect(200);

        // Verify the response has the expected structure
        expect(response.body).to.have.property("grantedPermissions");
        expect(response.body.grantedPermissions).to.be.an("array");
    }
}
