{"/entities/{path}/users/{username}/permissions": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["permissions", "permissions:view"]}], "tags": ["Permissions"], "summary": "Gets the list of user permissions by path", "responses": {"200": {"description": "User permissions", "schema": {"$ref": "#/definitions/UserPermissionsInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/users/{username}/permissions": {"parameters": [{"$ref": "#/parameters/username"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:permissions", "keyentity:permissions:view"]}], "tags": ["Permissions"], "summary": "Gets key entity's user's permissions", "responses": {"200": {"description": "User permissions", "schema": {"$ref": "#/definitions/UserPermissionsInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/permissions": {"get": {"security": [{"apiKey": []}, {"Permissions": []}], "tags": ["Permissions"], "summary": "Gets permissions descriptions", "responses": {"200": {"description": "List of permissions", "schema": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "description": {"type": "string"}}}}, "examples": {"application/json": [{"code": "entity", "group": "entity", "description": "Entity management"}, {"code": "entity:create", "group": "entity", "description": "Create entity"}, {"code": "entity:change-state", "group": "entity", "description": "Change the status of entity"}]}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/operations": {"get": {"security": [{"apiKey": []}, {"Permissions": []}], "tags": ["Permissions"], "summary": "Gets operations descriptions", "parameters": [{"name": "permission", "in": "query", "description": "Permission name", "required": false, "type": "string"}], "responses": {"200": {"description": "List of operations", "schema": {"$ref": "#/definitions/OperationDescriptions"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/availableOperations": {"get": {"security": [{"apiKey": []}, {"Permissions": []}], "tags": ["Permissions"], "summary": "Gets operations available for the current user", "responses": {"200": {"description": "List of operations", "schema": {"$ref": "#/definitions/OperationDescriptions"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/roles": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:role", "keyentity:role:view"]}], "tags": ["Permissions"], "summary": "Gets list of roles", "responses": {"200": {"description": "Role information", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedRoleSchema"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:role", "keyentity:role:create"]}], "tags": ["Permissions"], "summary": "Create role", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddItemRoleData"}}], "responses": {"201": {"description": "Added item object", "schema": {"$ref": "#/definitions/DetailedRoleSchema"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "500": {"description": "- 621: Role record not created\n"}}}}, "/roles/{roleId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:role", "keyentity:role:view"]}], "tags": ["Permissions"], "summary": "Get role by public id", "parameters": [{"$ref": "#/parameters/roleId"}], "responses": {"200": {"description": "Role information", "schema": {"$ref": "#/definitions/DetailedRoleSchema"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 623: Role not exist\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:role", "keyentity:role:delete"]}], "tags": ["Permissions"], "summary": "Remove role", "parameters": [{"$ref": "#/parameters/roleId"}, {"name": "force", "in": "query", "description": "Force to delete from all users", "required": false, "type": "boolean"}], "responses": {"204": {"description": "Record has been deleted"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 623: Role not exist\n"}, "409": {"description": "- 624: Failed to delete role with linked users. Need force flag\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:role", "keyentity:role:edit", "keyentity:role:move"]}], "tags": ["Permissions"], "summary": "Updates role", "parameters": [{"$ref": "#/parameters/roleId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateItemRoleData"}}], "responses": {"200": {"description": "Update role's information", "schema": {"$ref": "#/definitions/DetailedRoleSchema"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 218: Bad query for updating role\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/roles": {"get": {"security": [{"apiKey": []}, {"Permissions": ["user", "role", "role:view"]}], "tags": ["Permissions"], "summary": "Gets list of roles by path", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "Role information", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedRoleSchema"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["role", "role:create"]}], "tags": ["Permissions"], "summary": "Create role by path", "parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddItemRoleData"}}], "responses": {"201": {"description": "Added item object", "schema": {"$ref": "#/definitions/DetailedRoleSchema"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}, "500": {"description": "- 621: Role record not created\n"}}}}, "/entities/{path}/roles/{roleId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["user", "role", "role:view"]}], "tags": ["Permissions"], "summary": "Get role by public id by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roleId"}], "responses": {"200": {"description": "Role information", "schema": {"$ref": "#/definitions/DetailedRoleSchema"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 623: Role not exist\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["role", "role:delete"]}], "tags": ["Permissions"], "summary": "Remove role by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roleId"}, {"name": "force", "in": "query", "description": "Force to delete from all users", "required": false, "type": "boolean"}], "responses": {"204": {"description": "Record has been deleted"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 623: Role not exist\n"}, "409": {"description": "- 624: Failed to delete role with linked users. Need force flag\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["role", "role:edit", "role:move"]}], "tags": ["Permissions"], "summary": "Updates role by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roleId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateItemRoleData"}}], "responses": {"200": {"description": "Update role's information", "schema": {"$ref": "#/definitions/DetailedRoleSchema"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 218: Bad query for updating role\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/children/roles": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:role", "keyentity:role:view"]}], "tags": ["Permissions"], "summary": "Gets list of roles", "responses": {"200": {"description": "Role information", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedRoleSchema"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/children/roles": {"get": {"security": [{"apiKey": []}, {"Permissions": ["user", "role", "role:view"]}], "tags": ["Permissions"], "summary": "Gets list of children roles by path", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "Role information", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedRoleSchema"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/filteredPermissions": {"get": {"security": [{"apiKey": []}, {"Permissions": []}], "tags": ["Permissions"], "summary": "Gets filtered user permissions based on entity settings for UI tab visibility", "description": "Returns user permissions filtered according to entity settings that control UI tab visibility (disableCasinoHub, disableEngagementHub, enableAnalyticsOnly)", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "Filtered user permissions", "schema": {"$ref": "#/definitions/UserPermissions"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}