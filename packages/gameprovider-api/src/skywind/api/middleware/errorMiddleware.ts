import { buildLogData, getRequestInfoFomRequest, IpHolder } from "../../utils/requestHelper";
import { FastifyReply, FastifyRequest } from "fastify";
import { measures, token as jwt, logging } from "@skywind-group/sw-utils";
import { GameProviderAPIErrors } from "../../errors";
import Translation, { TranslateHolder } from "@skywind-group/sw-management-i18n";
import { SWError, GameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import Logger = logging.Logger;

function handleError(err, reqData: any, logger): SWError {
    let error;

    if (err instanceof SWError) {
        // Check whether this error is specifically marked to be forwarded to the wrapper (for external games)
        if (reqData?.body?.gameToken) {
            const gameTokenData = jwt.parse<GameTokenData>(reqData.body.gameToken);
            // Set the forwardToWrapper flag to true in case the error contains extraData
            // or in case of reality check errors (error code >= 1500)
            if (gameTokenData?.forwardToWrapper && (err.extraData || err.code >= 1500)) {
                err.setForwardToWrapper(true);
            }
        }
        logger[err.getErrorLevel()](err, "SWError");
        error = err;
    } else if (err.validation) {
        error = new GameProviderAPIErrors.ValidationError(err.message);
    } else if (err instanceof SyntaxError) {
        const syntaxErrorReason = err.message ? err.message : "N/A";
        logger.warn(err, `Malformed json - ${syntaxErrorReason}`);
        error = new GameProviderAPIErrors.MalformedJsonError(syntaxErrorReason);
    } else {
        logger.error(err, "Internal error");
        error = new GameProviderAPIErrors.InternalServerError(err);
    }

    return error;
}

export function createFastifyErrorHandler(logger: Logger) {
    return (err: any,
            req: FastifyRequest & IpHolder,
            reply: FastifyReply & TranslateHolder): any => {

        const loggerHook = () => {
            const reqData = buildLogData(
                req.url,
                req.method,
                req.query,
                req.body,
                getRequestInfoFomRequest(req)
            );

            measures.measureProvider.saveError(err);

            if (reply.sent) {
                logger.warn(err, { reqData }, "HTTP headers have already been sent", reqData);
                return;
            }

            if (err) {
                const error = handleError(err, reqData, logger);
                reply.code(error.responseStatus).send(Translation.getErrorResponse(error, reply));
            }
        };
        return measures.measureProvider.getContext() ?
            measures.measureProvider.trackTransaction(loggerHook) :
            measures.measureProvider.runInTransaction("fastify-error-handler", loggerHook);
    };
}
